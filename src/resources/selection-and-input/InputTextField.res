module EraseAppender = {
  @react.component
  let make = (~onRequestInputFocus, ~onRequestInputClear) =>
    <IconButton
      name=#close_medium
      size=17.
      color=Colors.neutralColor25
      hoveredColor=Colors.neutralColor50
      onPress={_ => {
        onRequestInputFocus()
        onRequestInputClear()
      }}
    />
}

module ListAppender = {
  @react.component
  let make = (
    ~inputFocused,
    ~inputErrored,
    ~inputHovered,
    ~disabled,
    ~opened,
    ~toggleButtonProps,
  ) => {
    let (ref, hovered) = Hover.use()
    let (pressed, setPressed) = React.useState(() => false)

    let toggleButtonProps = ReactAria.mergeProps2(
      toggleButtonProps,
      {
        ReactAria.Button.elementType: #div,
        disabled,
        onPressStart: _ => setPressed(_ => true),
        onPressEnd: _ => setPressed(_ => false),
      },
    )
    let style = StyleX.style(
      ~display=#flex,
      ~justifyContent=#center,
      ~alignItems=#center,
      ~flex="0",
      ~height="100%",
      ~flexBasis="40px",
      ~marginRight="-12px",
      ~borderLeft="1px solid",
      ~borderLeftColor=TextFieldStyle.borderColor(
        ~disabled,
        ~errored=inputErrored,
        ~focused=inputFocused,
        ~hovered=inputHovered,
      ),
      ~backgroundColor=if disabled {
        Colors.neutralColor05
      } else if pressed {
        Colors.neutralColor10
      } else if hovered || opened {
        Colors.neutralColor05
      } else {
        Colors.transparent
      },
      (),
    )
    let iconFillColor = if disabled {
      Colors.neutralColor20
    } else if hovered || opened {
      Colors.neutralColor90
    } else {
      Colors.neutralColor50
    }

    <Touchable style disabled ariaProps=toggleButtonProps ref onPress={_ => ()}>
      <Icon name=#arrow_down_light size=20. fill=iconFillColor />
    </Touchable>
  }
}

let style = (~focused, ~hovered, ~errored, ~disabled, ~bordered) =>
  ReactDOM.Style.make(
    ~height="40px",
    ~overflow="hidden",
    ~display="flex",
    ~flexDirection="row",
    ~alignItems="center",
    ~boxSizing="border-box",
    ~padding=`0 ${Spaces.normal->Float.toString}px`,
    ~columnGap=Spaces.small->Float.toString ++ "px",
    ~backgroundColor=TextFieldStyle.backgroundColor(~disabled),
    ~border=bordered ? "1px solid" : "none",
    ~borderColor=TextFieldStyle.borderColor(~focused, ~hovered, ~errored, ~disabled),
    ~borderRadius=TextFieldStyle.borderRadiusPx,
    (),
  )

let textInputStyle = (~disabled) =>
  ReactDOM.Style.make(
    ~flex="1", // NOTE - necessary on firefox/safari
    ~width="0", // NOTE - necessary on firefox/safari
    ~height="35px",
    ~color=TextFieldStyle.color(~disabled),
    ~fontSize=TextFieldStyle.fontSizePx,
    ~textOverflow="ellipsis",
    (),
  )

module OptionalField = {
  @react.component
  let make = React.forwardRef((
    ~children,
    ~label,
    ~required=?,
    ~fieldAction=?,
    ~tooltip=?,
    ~errorMessage,
    ~labelProps,
    ref,
  ) =>
    <Field
      ref=?{ref->Js.Nullable.toOption}
      label
      labelAriaProps=labelProps
      ?required
      ?errorMessage
      ?tooltip
      action=?fieldAction>
      children
    </Field>
  )
}

type suggestionVariationProps = {
  opened: bool,
  toggleButtonDisabled: bool,
  toggleButtonProps: ReactAria.Button.props,
  onRequestClear: unit => unit,
}
type variation = [
  | #normal
  | #suggestion(suggestionVariationProps)
]

@react.component
let make = (
  ~variation=#normal,
  ~label: string,
  ~required=?,
  ~tooltip=?,
  ~fieldAction=?,
  ~disabled=false,
  ~bordered=true,
  ~focused=false,
  ~placeholder=?,
  ~secureTextEntry=?,
  ~autoTrim=?,
  ~autoFocus=?,
  ~errorMessage=?,
  ~containerRef=?,
  ~inputRef=?,
  ~ariaProps=?,
  ~value="",
  ~onChange=?,
  ~onFocus=?,
  ~onBlur=?,
) => {
  let inputRef = inputRef->Option.getWithDefault(React.useRef(Js.Nullable.null))
  let (ref, hovered) = Hover.use(~ref=?containerRef, ())
  let {labelProps, fieldProps} = ReactAria.Label.use(~props={label, \"aria-label": label})

  let focusInput = () =>
    switch inputRef.current->Js.Nullable.toOption {
    | Some(input) => ReactDomElement.focus(input)
    | None => ()
    }

  let errored = errorMessage->Option.isSome
  let style = style(~focused, ~hovered, ~errored, ~bordered, ~disabled)
  let textInputStyle = textInputStyle(~disabled)

  let value =
    ariaProps->Option.flatMap(ariaProps => ariaProps.JsxDOM.value)->Option.getWithDefault(value)
  let ariaProps = ReactAria.mergeProps2(ariaProps, fieldProps)

  <OptionalField label ?required ?tooltip ?fieldAction labelProps errorMessage ref>
    <div style>
      <TextInput
        ariaProps
        readOnly=disabled
        ?placeholder
        ?secureTextEntry
        ?autoTrim
        ?autoFocus
        style=textInputStyle
        inputRef={inputRef->ReactDOM.Ref.domRef}
        value
        ?onChange
        ?onFocus
        ?onBlur
      />
      {switch variation {
      | #suggestion({onRequestClear}) if value !== "" && !disabled =>
        <EraseAppender onRequestInputFocus=focusInput onRequestInputClear=onRequestClear />
      | _ => React.null
      }}
      {switch variation {
      | #suggestion({opened, toggleButtonDisabled: appenderDisabled, toggleButtonProps}) =>
        <ListAppender
          disabled={appenderDisabled || disabled}
          inputFocused=focused
          inputErrored=errored
          inputHovered=hovered
          opened
          toggleButtonProps
        />
      | _ => React.null
      }}
    </div>
  </OptionalField>
}

let make = React.memo(make)
