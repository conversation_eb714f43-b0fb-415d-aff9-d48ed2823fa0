type item<'value> = {
  key: string,
  label: string,
  value: 'value,
}

@react.component
let make = (
  ~label,
  ~required=false,
  ~disabled=false,
  ~loading=false,
  ~placeholder=?,
  ~errorMessage=?,
  ~sectionTitle=Intl.t("Search results"),
  ~noResultLabel=Intl.t("No result found"),
  ~showResults=true,
  ~items: array<item<'value>>,
  ~value as inputValue,
  ~onInputChange,
  ~onSelectionChange,
) => {
  let (focused, setFocused) = React.useState(() => false)

  let triggerRef = React.useRef(Js.Nullable.null)
  let inputRef = React.useRef(Js.Nullable.null)
  let popoverRef = React.useRef(Js.Nullable.null)
  let listBoxRef = React.useRef(Js.Nullable.null)

  let showResults = showResults && !loading
  let children = React.useMemo3(() =>
    if showResults {
      [
        <ReactStately.Collection.Section title={sectionTitle->React.string}>
          {items
          ->Array.map(item =>
            <ReactStately.Collection.Item key=item.key textValue=item.label>
              <span> {item.label->React.string} </span>
            </ReactStately.Collection.Item>
          )
          ->React.array}
        </ReactStately.Collection.Section>,
      ]
    } else {
      []
    }
  , (items, sectionTitle, showResults))

  let onFocusChange = React.useCallback0(focused => setFocused(_ => focused))
  let onSelectionChange = React.useCallback1(key =>
    key
    ->Js.Nullable.toOption
    ->Option.flatMap(key => items->Array.getBy(item => item.key === key))
    ->Option.forEach(onSelectionChange)
  , [items])
  let props = {
    ReactStately.ComboBox.children,
    \"aria-label": label,
    disabled,
    ?placeholder,
    menuTrigger: #input,
    allowsCustomValue: true,
    allowsEmptyCollection: false,
    inputValue,
    onInputChange,
    onSelectionChange,
    onFocusChange,
  }

  let state = ReactStately.ComboBox.useState(~props)
  let {inputProps, listBoxProps} = ReactAria.ComboBox.use(
    ~props={
      ...props,
      inputRef: inputRef->ReactDOM.Ref.domRef,
      popoverRef: popoverRef->ReactDOM.Ref.domRef,
      listBoxRef: listBoxRef->ReactDOM.Ref.domRef,
    },
    ~state,
  )

  React.useEffect1(() => {
    if focused && state.inputValue !== "" {
      // NOTE - make difference between an input from the user or programmatically
      Js.Global.setTimeout(() => {
        state.onRequestClose()
      }, 5)->ignore
    }
    None
  }, [state.selectedKey])

  let onRequestClearSearch = React.useCallback1(() =>
    if state.inputValue !== "" {
      state.setInputValue("")
    }
  , [state.inputValue])

  let errored = errorMessage->Option.isSome
  let openedList = state.opened && state.inputValue !== ""

  <>
    <Field label required ?errorMessage>
      <InputSearch
        loading
        onRequestClear=onRequestClearSearch
        disabled
        focused
        ?placeholder
        containerRef=triggerRef
        inputRef
        ariaProps=inputProps
      />
    </Field>
    {if openedList {
      <Popover
        modal=false
        layout=#triggerStrictWidth
        offset=?{errored ? Some(-16.) : None}
        popoverRef
        triggerRef
        state={
          opened: state.opened,
          onRequestToggle: state.onRequestToggle,
          onRequestClose: state.onRequestClose,
        }>
        <ListBox
          noResultLabel
          domRef={listBoxRef->ReactDOM.Ref.domRef}
          props=listBoxProps
          state={state->ReactStately.ListBox.fromComboBoxState}
        />
      </Popover>
    } else {
      React.null
    }}
  </>
}
