external toReactRef: ReactDOM.domRef => React.ref<Js.Nullable.t<'a>> = "%identity"

let defaultStyle = ReactDOM.Style.make(
  ~minWidth="112px",
  ~minHeight="56px",
  ~resize="none",
  ~border="none",
  ~backgroundColor="inherit",
  ~cursor="inherit",
  ~fontFamily="inherit",
  (),
)

@react.component
let make = (
  ~areaRef=?,
  ~ariaProps=?,
  ~placeholder=Intl.t("Enter text..."),
  ~readOnly=false,
  ~style=?,
  ~value=?,
  ~onChange=?,
  ~onFocus=?,
  ~onBlur=?,
) => {
  let domRef = areaRef->Option.getWithDefault(React.useRef(Js.Nullable.null)->ReactDOM.Ref.domRef)

  let onHeightChange = React.useCallback0(element => {
    let elementStyleDict = Obj.magic(element)->Js.Dict.get("style")

    elementStyleDict->Option.map(dict => dict->Js.Dict.set("overflow", "hidden"))->ignore
    elementStyleDict->Option.map(dict => dict->Js.Dict.set("height", "auto"))->ignore

    let offsetHeight = element->WebAPI.DomElement.offsetHeight->Float.toInt
    let scrollHeight = element->WebAPI.DomElement.scrollHeight
    let clientHeight = element->WebAPI.DomElement.clientHeight
    let unsafeHeight = `${(scrollHeight + (offsetHeight - clientHeight))->Int.toString}px`

    elementStyleDict->Option.map(dict => dict->Js.Dict.set("height", unsafeHeight))->ignore
    elementStyleDict->Option.map(dict => dict->Js.Dict.set("overflow", "auto"))->ignore
  })

  React.useLayoutEffect3(() => {
    let ref = domRef->toReactRef
    switch ref.current->Js.Nullable.toOption {
    | Some(area) => onHeightChange(area)
    | None => ()
    }
    None
  }, (onHeightChange, areaRef, value))

  let (defaultValue, value) = value->Option.isSome ? (None, value) : (value, None)
  let props = {
    ReactAria.TextField.inputElementType: #textarea,
    \"aria-label": "TextArea",
    placeholder,
    readOnly,
    ?defaultValue,
    ?value,
    ?onChange,
    ?onFocus,
    ?onBlur,
  }
  let {inputProps} = ReactAria.TextField.use(~props, ~ref=domRef)

  let props = ReactAria.mergeProps2(inputProps, ariaProps)
  let combinedStyle = switch style {
  | Some(style) => ReactDOM.Style.combine(defaultStyle, style)
  | None => defaultStyle
  }

  <ReactAria.Spread props>
    <textarea style=combinedStyle ref=domRef />
  </ReactAria.Spread>
}

let make = React.memo(make)
