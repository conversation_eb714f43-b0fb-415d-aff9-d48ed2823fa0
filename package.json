{"name": "app", "version": "0.0.0", "private": true, "author": "Wino Technologies", "type": "module", "engines": {"node": ">=22.12.0"}, "scripts": {"deps": "yarn --check-files --frozen-lockfile", "postinstall": "yarn clean", "up": "yarn upgrade-interactive --latest && yarn postinstall", "build": "yarn deps && rescript format -check -all && rewatch build && tsc -b tsconfig.json && vite build", "storybook": "storybook build", "test": "vitest run", "format": "rescript format -all", "dev:test": "DEBUG_PRINT_LIMIT=100000 vitest watch --ui --api.host --api.port 5000", "dev:server": "vite --host", "dev:storybook": "storybook dev -p 7007", "dev:res": "rewatch watch", "dev:ts": "tsc -b tsconfig.json -w", "graphql:introspection": "npx get-graphql-schema https://xx.wino.fr/graphql -j > graphql_schema_gateway.json -h 'Authorization=Bearer xxx'", "clean": "find ./src -name '*.res.js' -type f -delete && find ./tests -name '*.res.js' -type f -delete && rewatch clean"}, "dependencies": {"@apollo/client": "3.4.17", "@internationalized/date": "3.8.2", "@reasonml-community/graphql-ppx": "1.2.4-79d140a5.0", "@rescript/react": "0.13.0", "@sentry/react": "10.5.0", "@stylexjs/stylex": "0.15.3", "@wino/accounting": "latest", "@wino/translator": "latest", "exceljs": "4.3.0", "graphql": "16.11.0", "history": "4.10.1", "intl": "1.2.5", "jwt-decode": "3.1.2", "libphonenumber-js": "1.12.13", "logrocket": "3.0.1", "logrocket-react": "5.0.1", "moment": "2.30.1", "papaparse": "5.4.1", "posthog-js": "1.260.1", "qs": "6.14.0", "react": "18.3.1", "react-aria": "3.42.0", "react-dates": "21.8.0", "react-dom": "18.3.1", "react-router": "5.2.1", "react-router-dom": "5.3.0", "react-stately": "3.40.0", "rescript": "11.1.4", "rescript-apollo-client": "2.4.1", "rescript-future": "2.1.0", "uuid": "9.0.1"}, "devDependencies": {"@rolandpeelen/rewatch": "1.2.2", "@storybook/addon-actions": "8.3.2", "@storybook/addon-docs": "8.3.2", "@storybook/addon-storysource": "8.3.2", "@storybook/components": "8.3.2", "@storybook/core-events": "8.3.2", "@storybook/react": "8.3.2", "@storybook/react-vite": "8.3.2", "@testing-library/dom": "10.4.1", "@testing-library/jest-dom": "6.8.0", "@testing-library/react": "16.3.0", "@testing-library/react-hooks": "8.0.1", "@testing-library/user-event": "14.6.1", "@types/react": "18.3.18", "@types/react-dom": "18.3.5", "@types/react-router-dom": "5.3.3", "@vitejs/plugin-react": "5.0.0", "@vitest/coverage-v8": "3.2.4", "@vitest/ui": "3.2.4", "blob-polyfill": "9.0.20240710", "cross-fetch": "4.1.0", "jsdom": "26.1.0", "lenses-ppx": "6.1.10", "msw": "1.3.3", "storybook": "8.3.2", "typescript": "5.9.2", "vite": "7.1.3", "vite-plugin-stylex": "0.13.0", "vitest": "3.2.4"}}